//+------------------------------------------------------------------+
//|                                                RunAllTests.mqh  |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "unit/TestPipelineResult.mqh"
#include "unit/TestTradingPipeline.mqh"
#include "unit/TestMainPipeline.mqh"
#include "unit/TestTradingPipelineContainerBase.mqh"
#include "unit/TestTradingPipelineContainer.mqh"
#include "unit/TestTradingPipelineContainerManager.mqh"
#include "unit/TestTradingPipelineRegistry.mqh"
#include "unit/TestTradingPipelineExplorer.mqh"
#include "unit/TestTradingPipelineDriver.mqh"
#include "unit/TestTradingController.mqh"
#include "unit/TestTradingPipelineDriverBase.mqh"
#include "unit/TestTradingPipelineContainerLoggerDecorator.mqh"
#include "unit/TestTradingPipelineDriverLoggerDecorator.mqh"

//+------------------------------------------------------------------+
//| 字符串重複函數                                                     |
//+------------------------------------------------------------------+
string StringRepeat(string str, int count)
{
    string result = "";
    for(int i = 0; i < count; i++)
    {
        result = result + str;
    }
    return result;
}

//+------------------------------------------------------------------+
//| 運行所有 PipelineAdvance_v1 測試                                  |
//+------------------------------------------------------------------+
void RunAllPipelineAdvanceV1Tests()
{
    Print("\n" + StringRepeat("=", 70));
    Print("  PipelineAdvance_v1 完整測試套件");
    Print(StringRepeat("=", 70));
    Print("開始時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 70));

    // 運行單元測試
    RunPipelineAdvanceV1UnitTests();

    // 運行整合測試
    RunPipelineAdvanceV1IntegrationTests();

    // 運行增強版整合測試
    RunPipelineAdvanceV1IntegrationTestsV2();

    // 運行容器整合測試
    RunTradingPipelineContainerIntegrationTests();

    Print("\n" + StringRepeat("=", 70));
    Print("  PipelineAdvance_v1 完整測試套件完成");
    Print("結束時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 70));
}

//+------------------------------------------------------------------+
//| 運行單元測試                                                       |
//+------------------------------------------------------------------+
void RunPipelineAdvanceV1UnitTests()
{
    Print("\n--- 開始執行 PipelineAdvance_v1 單元測試 ---");

    // 運行 PipelineResult 測試
    TestRunner* resultRunner = new TestRunner();
    TestPipelineResult* resultTest = new TestPipelineResult(resultRunner);
    resultRunner.RunTestCase(resultTest);
    resultRunner.ShowSummary();
    delete resultTest;
    delete resultRunner;

    // 運行 TradingPipeline 測試
    TestRunner* pipelineRunner = new TestRunner();
    TestTradingPipeline* pipelineTest = new TestTradingPipeline(pipelineRunner);
    pipelineRunner.RunTestCase(pipelineTest);
    pipelineRunner.ShowSummary();
    delete pipelineTest;
    delete pipelineRunner;

    // 運行 MainPipeline 測試
    TestRunner* mainPipelineRunner = new TestRunner();
    TestMainPipeline* mainPipelineTest = new TestMainPipeline(mainPipelineRunner);
    mainPipelineRunner.RunTestCase(mainPipelineTest);
    mainPipelineRunner.ShowSummary();
    delete mainPipelineTest;
    delete mainPipelineRunner;

    // 運行 TradingPipelineContainerBase 測試
    TestRunner* containerBaseRunner = new TestRunner();
    TestTradingPipelineContainerBase* containerBaseTest = new TestTradingPipelineContainerBase(containerBaseRunner);
    containerBaseRunner.RunTestCase(containerBaseTest);
    containerBaseRunner.ShowSummary();
    delete containerBaseTest;
    delete containerBaseRunner;

    // 運行 TradingPipelineContainer 測試
    TestRunner* containerRunner = new TestRunner();
    TestTradingPipelineContainer* containerTest = new TestTradingPipelineContainer(containerRunner);
    containerRunner.RunTestCase(containerTest);
    containerRunner.ShowSummary();
    delete containerTest;
    delete containerRunner;

    // 運行 TradingPipelineContainerManager 測試
    TestRunner* managerRunner = new TestRunner();
    TestTradingPipelineContainerManager* managerTest = new TestTradingPipelineContainerManager(managerRunner);
    managerRunner.RunTestCase(managerTest);
    managerRunner.ShowSummary();
    delete managerTest;
    delete managerRunner;

    // 運行 TradingPipelineRegistry 測試
    TestRunner* registryRunner = new TestRunner();
    TestTradingPipelineRegistry* registryTest = new TestTradingPipelineRegistry(registryRunner);
    registryRunner.RunTestCase(registryTest);
    registryRunner.ShowSummary();
    delete registryTest;
    delete registryRunner;

    // 運行 TradingPipelineExplorer 測試
    TestRunner* explorerRunner = new TestRunner();
    TestTradingPipelineExplorer* explorerTest = new TestTradingPipelineExplorer(explorerRunner);
    explorerRunner.RunTestCase(explorerTest);
    explorerRunner.ShowSummary();
    delete explorerTest;
    delete explorerRunner;

    // 運行 TradingPipelineDriver 測試
    TestRunner* driverRunner = new TestRunner();
    TestTradingPipelineDriver* driverTest = new TestTradingPipelineDriver(driverRunner);
    driverRunner.RunTestCase(driverTest);
    driverRunner.ShowSummary();
    delete driverTest;
    delete driverRunner;

    // 運行 TradingController 測試
    TestRunner* controllerRunner = new TestRunner();
    TestTradingController* controllerTest = new TestTradingController(controllerRunner);
    controllerRunner.RunTestCase(controllerTest);
    controllerRunner.ShowSummary();
    delete controllerTest;
    delete controllerRunner;

    // 運行 TradingPipelineDriverBase 測試
    TestRunner* driverBaseRunner = new TestRunner();
    TestTradingPipelineDriverBase* driverBaseTest = new TestTradingPipelineDriverBase(driverBaseRunner);
    driverBaseRunner.RunTestCase(driverBaseTest);
    driverBaseRunner.ShowSummary();
    delete driverBaseTest;
    delete driverBaseRunner;

    // 運行 TradingPipelineContainerLoggerDecorator 測試
    TestRunner* containerLoggerRunner = new TestRunner();
    TestTradingPipelineContainerLoggerDecorator* containerLoggerTest = new TestTradingPipelineContainerLoggerDecorator(containerLoggerRunner);
    containerLoggerRunner.RunTestCase(containerLoggerTest);
    containerLoggerRunner.ShowSummary();
    delete containerLoggerTest;
    delete containerLoggerRunner;

    // 運行 TradingPipelineDriverLoggerDecorator 測試
    TestRunner* driverLoggerRunner = new TestRunner();
    TestTradingPipelineDriverLoggerDecorator* driverLoggerTest = new TestTradingPipelineDriverLoggerDecorator(driverLoggerRunner);
    driverLoggerRunner.RunTestCase(driverLoggerTest);
    driverLoggerRunner.ShowSummary();
    delete driverLoggerTest;
    delete driverLoggerRunner;

    Print("--- PipelineAdvance_v1 單元測試完成 ---");
}

//+------------------------------------------------------------------+
//| 運行整合測試                                                       |
//+------------------------------------------------------------------+
void RunPipelineAdvanceV1IntegrationTests()
{
    Print("\n--- 開始執行 PipelineAdvance_v1 整合測試 ---");
    Print("⚠️ 整合測試目錄已清空，暫無可執行的整合測試");
    Print("--- PipelineAdvance_v1 整合測試完成 ---");
}

//+------------------------------------------------------------------+
//| 運行增強版整合測試 (v2)                                           |
//+------------------------------------------------------------------+
void RunPipelineAdvanceV1IntegrationTestsV2()
{
    Print("\n--- 開始執行 PipelineAdvance_v1 增強版整合測試 (v2) ---");
    Print("⚠️ 整合測試目錄已清空，暫無可執行的整合測試");
    Print("--- PipelineAdvance_v1 增強版整合測試 (v2) 完成 ---");
}

//+------------------------------------------------------------------+
//| 運行 TradingPipelineContainer 整合測試                            |
//+------------------------------------------------------------------+
void RunTradingPipelineContainerIntegrationTests()
{
    Print("\n--- 開始執行 TradingPipelineContainer 整合測試 ---");
    Print("⚠️ 整合測試目錄已清空，暫無可執行的整合測試");
    Print("--- TradingPipelineContainer 整合測試完成 ---");
}

//+------------------------------------------------------------------+
//| 運行所有測試（簡化版）                                             |
//+------------------------------------------------------------------+
void RunAllPipelineAdvanceV1TestsSimple()
{
    Print("\n📄 開始執行 PipelineAdvance_v1 完整測試套件...");

    // 運行單元測試
    RunPipelineAdvanceV1UnitTests();

    // 運行整合測試
    RunPipelineAdvanceV1IntegrationTests();

    // 運行增強版整合測試
    RunPipelineAdvanceV1IntegrationTestsV2();

    // 運行容器整合測試
    RunTradingPipelineContainerIntegrationTests();

    Print("✅ PipelineAdvance_v1 完整測試套件執行完成");
}

//+------------------------------------------------------------------+
//| 快速測試檢查                                                       |
//+------------------------------------------------------------------+
bool QuickPipelineAdvanceV1Check()
{
    Print("⚡ 開始快速 PipelineAdvance_v1 測試檢查...");

    bool unitTestsPassed = true;
    bool integrationTestsPassed = true;

    // 快速單元測試
    TestPipelineResult* resultTest = new TestPipelineResult();
    resultTest.RunTests();
    delete resultTest;

    TestTradingPipeline* pipelineTest = new TestTradingPipeline();
    pipelineTest.RunTests();
    delete pipelineTest;

    TestMainPipeline* mainPipelineTest = new TestMainPipeline();
    mainPipelineTest.RunTests();
    delete mainPipelineTest;

    TestTradingPipelineDriver* driverTest = new TestTradingPipelineDriver();
    driverTest.RunTests();
    delete driverTest;

    TestTradingController* controllerTest = new TestTradingController();
    controllerTest.RunTests();
    delete controllerTest;

    // 快速測試新增的類
    TestTradingPipelineDriverBase* driverBaseTest = new TestTradingPipelineDriverBase();
    driverBaseTest.RunTests();
    delete driverBaseTest;

    // 整合測試已清空，跳過
    Print("⚠️ 整合測試目錄已清空，跳過整合測試檢查");
    integrationTestsPassed = true;  // 假設通過，因為沒有測試
    bool integrationTestsV2Passed = true;  // 假設通過，因為沒有測試

    bool allPassed = unitTestsPassed && integrationTestsPassed && integrationTestsV2Passed;

    if(allPassed)
    {
        Print("✅ 快速測試檢查通過");
    }
    else
    {
        Print("❌ 快速測試檢查失敗");
    }

    return allPassed;
}

//+------------------------------------------------------------------+
//| 運行 SimpleTestRunner_v2 專用測試                                 |
//+------------------------------------------------------------------+
void RunSimpleTestRunnerV2Only()
{
    Print("\n🚀 開始執行 SimpleTestRunner_v2 專用測試...");
    Print("⚠️ 整合測試目錄已清空，暫無可執行的整合測試");
    Print("✅ SimpleTestRunner_v2 專用測試執行完成");
}

//+------------------------------------------------------------------+
//| 比較 SimpleTestRunner 和 SimpleTestRunner_v2                     |
//+------------------------------------------------------------------+
void CompareSimpleTestRunners()
{
    Print("\n🔍 開始比較 SimpleTestRunner 和 SimpleTestRunner_v2...");
    Print("⚠️ 整合測試目錄已清空，無法進行版本比較");
    Print("💡 建議：重新實現整合測試後再進行版本比較");
    Print("🔍 SimpleTestRunner 比較完成");
}

//+------------------------------------------------------------------+
//| PipelineGroupManager 專項測試                                     |
//+------------------------------------------------------------------+
void RunPipelineGroupManagerFocusedTests()
{
    Print("\n🎯 開始執行 PipelineGroupManager 專項測試...");

    Print("--- 單元測試：TradingPipelineContainerManager ---");
    TestTradingPipelineContainerManager* unitTest = new TestTradingPipelineContainerManager();
    unitTest.RunTests();
    delete unitTest;

    Print("--- 整合測試已清空，跳過整合測試部分 ---");
    Print("⚠️ 整合測試目錄已清空，暫無可執行的整合測試");

    Print("✅ PipelineGroupManager 專項測試完成");
}

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    // 默認運行所有測試
    RunAllPipelineAdvanceV1Tests();
}

